//+------------------------------------------------------------------+
//|                                                   TestRunner.mq4 |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含測試文件
#include "TestCompositePipeline.mqh"
#include "TestPipelineGroupManager.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 開始執行 PipelineAdvance_v1 測試套件 ===");

    // 執行 CompositePipeline 測試
    RunCompositePipelineTests();

    // 執行 PipelineGroupManager 測試
    RunPipelineGroupManagerTests();

    Print("=== 測試套件執行完成 ===");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("測試程序結束，原因代碼: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 測試程序不需要在 tick 中執行任何操作
}

//+------------------------------------------------------------------+
//| 執行 CompositePipeline 測試                                      |
//+------------------------------------------------------------------+
void RunCompositePipelineTests()
{
    Print("\n=== 開始執行 CompositePipeline 測試 ===");

    TestCompositePipeline* test = new TestCompositePipeline();

    if(test != NULL)
    {
        test.RunTests();
        delete test;
        Print("CompositePipeline 測試執行完成");
    }
    else
    {
        Print("ERROR: 無法創建 TestCompositePipeline 實例");
    }

    Print("=== CompositePipeline 測試結束 ===\n");
}

//+------------------------------------------------------------------+
//| 執行 PipelineGroupManager 測試                                  |
//+------------------------------------------------------------------+
void RunPipelineGroupManagerTests()
{
    Print("\n=== 開始執行 PipelineGroupManager 測試 ===");

    TestPipelineGroupManager* test = new TestPipelineGroupManager();

    if(test != NULL)
    {
        test.RunTests();
        delete test;
        Print("PipelineGroupManager 測試執行完成");
    }
    else
    {
        Print("ERROR: 無法創建 TestPipelineGroupManager 實例");
    }

    Print("=== PipelineGroupManager 測試結束 ===\n");
}
