//+------------------------------------------------------------------+
//|                                           CompositePipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib-master/Collection/Vector.mqh"
#include "TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 複合流水線類                                                     |
//+------------------------------------------------------------------+
class CompositePipeline : public ITradingPipeline
{
private:
    string m_name;                          // 流水線名稱
    string m_type;                          // 流水線類型
    bool m_executed;                        // 執行狀態
    Vector<ITradingPipeline*> m_pipelines;  // 子流水線向量
    bool m_owned;                           // 是否擁有子流水線
    int m_maxPipelines;                      // 最大子流水線數量
    PipelineResult* m_last_result;           // 執行結果

public:
    // 構造函數
    CompositePipeline(string name, string type = "CompositePipeline", bool owned = false, int maxPipelines = 20)
        : m_name(name),
          m_type(type),
          m_executed(false),
          m_pipelines(owned),
          m_maxPipelines(maxPipelines),
          m_last_result(new PipelineResult(false, "尚未執行", name, ERROR_LEVEL_INFO))
    {
    }

    // 析構函數
    virtual ~CompositePipeline()
    {
        Clear();
    }

    // 添加子流水線
    bool AddPipeline(ITradingPipeline* pipeline)
    {
        // 檢查子流水線是否為空或已達到最大數量限制
        if(pipeline == NULL || m_pipelines.size() >= m_maxPipelines)
        {
            m_last_result = new PipelineResult(false, "子流水線為空或已達到最大數量限制", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        if(!m_pipelines.add(pipeline))
        {
            m_last_result = new PipelineResult(false, "添加子流水線失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功添加子流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 移除子流水線
    bool RemovePipeline(ITradingPipeline* pipeline)
    {
        if(!m_pipelines.remove(pipeline))
        {
            m_last_result = new PipelineResult(false, "移除子流水線失敗", GetName(), ERROR_LEVEL_WARNING);
            return false;
        }

        m_last_result = new PipelineResult(true, "成功移除子流水線", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    // 按名稱移除子流水線
    bool RemovePipelineByName(string name)
    {
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            if(pipeline.GetName() == name)
            {
                m_last_result = new PipelineResult(true, "成功移除子流水線", GetName(), ERROR_LEVEL_INFO);
                return m_pipelines.remove(pipeline);
            }
        }

        m_last_result = new PipelineResult(false, "未找到名稱為 '" + name + "' 的子流水線", GetName(), ERROR_LEVEL_WARNING);
        return false;
    }

    // 清空所有子流水線
    void Clear()
    {
        m_pipelines.clear();
    }

    // 獲取子流水線數量
    int GetPipelineCount() const
    {
        return m_pipelines.size();
    }

    // 獲取最大子流水線數量
    int GetMaxPipelines() const
    {
        return m_maxPipelines;
    }

    // 按索引獲取子流水線
    ITradingPipeline* GetPipeline(int index, ITradingPipeline* parent)
    {
        if(index >= 0 && index < m_pipelines.size())
        {
            m_last_result = new PipelineResult(true, "成功獲取子流水線", GetName(), ERROR_LEVEL_INFO);
            return m_pipelines.get(index);
        }
        m_last_result = new PipelineResult(false, "索引超出範圍", GetName(), ERROR_LEVEL_WARNING);
        return parent;
    }

    // 按名稱查找子流水線
    ITradingPipeline* FindByName(string name, ITradingPipeline* parent)
    {
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            if(pipeline.GetName() == name)
            {
                m_last_result = new PipelineResult(true, "成功找到子流水線", GetName(), ERROR_LEVEL_INFO);
                return pipeline;
            }
        }
        m_last_result = new PipelineResult(false, "未找到名稱為 '" + name + "' 的子流水線", GetName(), ERROR_LEVEL_WARNING);
        return parent;
    }

    // 實現 ITradingPipeline 介面方法

    // 執行流水線
    virtual void Execute() override
    {
        if(m_executed) return;

        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            pipeline.Execute();
        }

        m_executed = true;
    }

    // 獲取流水線名稱
    virtual string GetName() override
    {
        return m_name;
    }

    // 獲取流水線類型
    virtual string GetType() override
    {
        return m_type;
    }

    // 檢查是否已執行
    virtual bool IsExecuted() override
    {
        return m_executed;
    }

    // 重置所有子流水線
    virtual void Restore() override
    {
        m_executed = false;
        foreachv(ITradingPipeline*, pipeline, m_pipelines)
        {
            pipeline.Restore();
        }
    }

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }
};
