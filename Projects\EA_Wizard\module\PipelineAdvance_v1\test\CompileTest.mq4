//+------------------------------------------------------------------+
//|                                                  CompileTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 測試編譯是否成功
#include "../PipelineGroupManager.mqh"
#include "../CompositePipeline.mqh"
#include "../PipelineGroup.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("編譯測試成功！");
    
    // 簡單的實例化測試
    PipelineGroupManager* manager = new PipelineGroupManager("測試管理器");
    Print("管理器創建成功: ", manager.GetName());
    
    CompositePipeline* composite = new CompositePipeline("測試複合流水線");
    Print("複合流水線創建成功: ", composite.GetName());
    
    PipelineGroup* group = new PipelineGroup("測試組", "測試描述", TRADING_TICK);
    Print("流水線組創建成功: ", group.GetName());
    
    // 清理
    delete manager;
    delete composite;
    delete group;
    
    Print("所有測試完成！");
}
//+------------------------------------------------------------------+
