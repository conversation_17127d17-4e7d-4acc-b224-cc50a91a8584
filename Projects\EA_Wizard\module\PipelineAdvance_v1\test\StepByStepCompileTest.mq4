//+------------------------------------------------------------------+
//|                                      StepByStepCompileTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 逐步測試編譯
#include "../TradingEvent.mqh"

// 測試 TradingPipeline
#include "../TradingPipeline.mqh"

// 測試具體的流水線實現
class TestPipeline : public TradingPipeline
{
public:
    TestPipeline(string name) : TradingPipeline(name, "TestPipeline", TICK_DATA_FEED, NULL) {}

protected:
    virtual void Main() override
    {
        Print("執行測試流水線: ", GetName());
    }
};

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 逐步編譯測試開始 ===");
    
    // 測試 1: 基本枚舉
    Print("\n--- 測試 1: 基本枚舉 ---");
    ENUM_TRADING_EVENT event = TRADING_TICK;
    ENUM_TRADING_STAGE stage = TICK_DATA_FEED;
    Print("事件: ", TradingEventUtils::EventToString(event));
    Print("階段: ", TradingEventUtils::StageToString(stage));
    
    // 測試 2: TradingPipeline
    Print("\n--- 測試 2: TradingPipeline ---");
    TestPipeline* pipeline = new TestPipeline("測試流水線");
    Print("流水線名稱: ", pipeline.GetName());
    Print("流水線類型: ", pipeline.GetType());
    Print("流水線階段: ", TradingEventUtils::StageToString(pipeline.GetStage()));
    
    // 執行流水線
    pipeline.Execute();
    Print("流水線已執行: ", pipeline.IsExecuted());
    
    // 清理
    delete pipeline;
    
    Print("\n=== 逐步編譯測試完成 ===");
}
//+------------------------------------------------------------------+
