# TestPipelineGroupManager 測試文檔

## 概述

`TestPipelineGroupManager.mqh` 是 PipelineAdvance_v1 模組中 `PipelineGroupManager` 類的完整單元測試套件。該測試套件驗證了 PipelineGroupManager 的所有核心功能，包括組管理、事件驅動執行、狀態管理等。

## 測試覆蓋範圍

### 1. 構造函數測試 (TestConstructor)
- 測試默認構造函數
- 測試自定義參數構造函數
- 驗證初始狀態設置

### 2. 基本屬性測試 (TestBasicProperties)
- 最大組數量限制 (3個)
- 初始組數量 (0個)
- 空位置檢查
- 啟用/禁用狀態管理

### 3. 添加組測試 (TestAddGroup)
- 成功添加組到空位置
- 達到最大容量時的行為
- 添加 NULL 組的錯誤處理
- 重複名稱組的防護機制

### 4. 移除組測試 (TestRemoveGroup)
- 成功移除存在的組
- 移除不存在組的錯誤處理
- 移除 NULL 組的錯誤處理
- 移除後狀態更新

### 5. 最大組數限制測試 (TestMaxGroupsLimit)
- 驗證最大 3 個組的限制
- 超過限制時的拒絕行為
- 空位置狀態的正確性

### 6. 事件類型執行測試 (TestExecuteByEventType)
- TRADING_INIT 事件執行
- TRADING_TICK 事件執行
- TRADING_DEINIT 事件執行
- 事件類型過濾機制
- 執行狀態管理

### 7. 事件類型重置測試 (TestRestoreByEventType)
- 按事件類型選擇性重置
- 重置後狀態驗證
- 管理器整體狀態管理

### 8. 按名稱查找組測試 (TestFindGroupByName)
- 查找存在的組
- 查找不存在組的處理
- 返回值驗證

### 9. 獲取所有組測試 (TestGetAllGroups)
- 空管理器的處理
- 獲取所有組的數組操作
- 數組內容驗證

### 10. 清理功能測試 (TestClear)
- 清理所有組
- 狀態重置驗證
- 內存管理

### 11. 邊界情況測試 (TestEdgeCases)
- 禁用狀態下的執行防護
- 重複執行防護機制
- 異常狀態處理

### 12. 複雜場景測試 (TestComplexScenario)
- 完整 EA 生命週期模擬
- 多事件類型工作流程
- 嵌套流水線結構
- 狀態轉換驗證

## 測試工具類

### TestTradingPipeline
簡化的測試流水線類，用於模擬真實的交易流水線行為：
- 可配置成功/失敗狀態
- 自定義執行消息
- 繼承自 TradingPipeline

## 運行測試

### 單獨運行
```mql4
// 使用專用運行器
#include "RunTestPipelineGroupManager.mq4"
```

### 集成運行
```mql4
// 通過主測試運行器
#include "TestRunner.mq4"

// 或通過完整測試套件
#include "../RunAllTests.mqh"
void OnStart() {
    RunAllPipelineAdvanceV1Tests();
}
```

## 測試結果

測試運行器會顯示詳細的測試結果，包括：
- 每個測試方法的通過/失敗狀態
- 失敗測試的詳細錯誤信息
- 測試摘要統計
- 總體成功率

## 依賴項

- `TestFramework.mqh` - 測試框架和斷言方法
- `PipelineGroupManager.mqh` - 被測試的主要類
- `PipelineGroup.mqh` - 流水線組類
- `CompositePipeline.mqh` - 複合流水線類
- `TradingPipeline.mqh` - 交易流水線基類

## 注意事項

1. **內存管理**: 測試中創建的所有對象都會在測試結束時正確清理
2. **狀態隔離**: 每個測試方法都是獨立的，不會影響其他測試
3. **錯誤處理**: 測試覆蓋了各種錯誤情況和邊界條件
4. **真實場景**: 複雜場景測試模擬了真實的 EA 使用情況

## 維護指南

當 PipelineGroupManager 類發生變更時，請相應更新測試：
1. 新增方法需要添加對應的測試方法
2. 修改現有方法需要更新相關測試
3. 確保測試覆蓋所有公共接口
4. 保持測試的可讀性和維護性
