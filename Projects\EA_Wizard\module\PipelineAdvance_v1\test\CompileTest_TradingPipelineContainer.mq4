//+------------------------------------------------------------------+
//|                          CompileTest_TradingPipelineContainer.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineContainer.mqh"
#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 簡單編譯測試                                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== TradingPipelineContainer 編譯測試 ===");
    
    // 測試基本創建
    TradingPipelineContainer* container = new TradingPipelineContainer(
        "測試容器", "編譯測試", "TestContainer", TRADING_TICK);
    
    if(container != NULL)
    {
        Print("✅ TradingPipelineContainer 創建成功");
        Print("容器名稱: ", container.GetName());
        Print("容器類型: ", container.GetType());
        Print("容器描述: ", container.GetDescription());
        Print("事件類型: ", container.GetEventType());
        Print("是否啟用: ", container.IsEnabled());
        Print("是否為空: ", container.IsEmpty());
        Print("最大容量: ", container.GetMaxPipelines());
        
        delete container;
        Print("✅ TradingPipelineContainer 銷毀成功");
    }
    else
    {
        Print("❌ TradingPipelineContainer 創建失敗");
    }
    
    // 測試管理器創建
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
        "測試管理器", "TestManager");
    
    if(manager != NULL)
    {
        Print("✅ TradingPipelineContainerManager 創建成功");
        Print("管理器名稱: ", manager.GetName());
        Print("管理器類型: ", manager.GetType());
        Print("是否啟用: ", manager.IsEnabled());
        Print("是否為空: ", manager.IsEmpty());
        Print("最大容量: ", manager.GetMaxContainers());
        
        delete manager;
        Print("✅ TradingPipelineContainerManager 銷毀成功");
    }
    else
    {
        Print("❌ TradingPipelineContainerManager 創建失敗");
    }
    
    Print("=== 編譯測試完成 ===");
}

//+------------------------------------------------------------------+
//| 專家顧問初始化函數                                               |
//+------------------------------------------------------------------+
int OnInit()
{
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 專家顧問反初始化函數                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| 專家顧問tick函數                                                 |
//+------------------------------------------------------------------+
void OnTick()
{
}
