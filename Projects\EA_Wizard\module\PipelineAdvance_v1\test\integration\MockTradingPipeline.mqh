//+------------------------------------------------------------------+
//|                                         MockTradingPipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 模擬交易流水線 - 用於測試                                         |
//+------------------------------------------------------------------+
class MockTradingPipeline : public TradingPipeline
{
private:
    bool m_shouldSucceed;        // 是否應該成功執行
    int m_executionDelay;        // 執行延遲（毫秒）
    string m_customMessage;      // 自定義消息
    int m_executionCount;        // 執行次數計數器
    bool m_throwException;       // 是否拋出異常

public:
    // 構造函數
    MockTradingPipeline(string name,
                       bool shouldSucceed = true,
                       int executionDelay = 0,
                       string customMessage = "",
                       string type = "MockTradingPipeline")
        : TradingPipeline(name, type),
          m_shouldSucceed(shouldSucceed),
          m_executionDelay(executionDelay),
          m_customMessage(customMessage),
          m_executionCount(0),
          m_throwException(false)
    {
        if(m_customMessage == "")
        {
            m_customMessage = shouldSucceed ? "模擬執行成功" : "模擬執行失敗";
        }
    }

    // 設置是否應該成功
    void SetShouldSucceed(bool shouldSucceed) { m_shouldSucceed = shouldSucceed; }

    // 設置執行延遲
    void SetExecutionDelay(int delay) { m_executionDelay = delay; }

    // 設置自定義消息
    void SetCustomMessage(string message) { m_customMessage = message; }

    // 設置是否拋出異常
    void SetThrowException(bool throwException) { m_throwException = throwException; }

    // 獲取執行次數
    int GetExecutionCount() const { return m_executionCount; }

    // 重置執行計數器
    void ResetExecutionCount() { m_executionCount = 0; }

protected:
    // 實現主要邏輯
    virtual void Main() override
    {
        m_executionCount++;

        // 模擬執行延遲
        if(m_executionDelay > 0)
        {
            Sleep(m_executionDelay);
        }

        // 模擬異常情況
        if(m_throwException)
        {
            Print("MockTradingPipeline: 模擬異常拋出 - " + GetName());
            return; // 在 MQL4 中無法真正拋出異常，只能提前返回
        }

        // 模擬執行結果
        if(m_shouldSucceed)
        {
            Print("MockTradingPipeline: 成功執行 - " + GetName() + " (" + m_customMessage + ")");
        }
        else
        {
            Print("MockTradingPipeline: 執行失敗 - " + GetName() + " (" + m_customMessage + ")");
        }
    }
};

//+------------------------------------------------------------------+
//| 模擬流水線工廠 - 創建各種類型的模擬流水線                          |
//+------------------------------------------------------------------+
class MockTradingPipelineFactory
{
public:
    // 創建成功的流水線
    static MockTradingPipeline* CreateSuccessfulPipeline(string name, int delay = 0)
    {
        return new MockTradingPipeline(name, true, delay, "成功執行");
    }

    // 創建失敗的流水線
    static MockTradingPipeline* CreateFailedPipeline(string name, int delay = 0)
    {
        return new MockTradingPipeline(name, false, delay, "執行失敗");
    }

    // 創建慢速流水線
    static MockTradingPipeline* CreateSlowPipeline(string name, int delay = 1000)
    {
        return new MockTradingPipeline(name, true, delay, "慢速執行");
    }

    // 創建異常流水線
    static MockTradingPipeline* CreateExceptionPipeline(string name)
    {
        MockTradingPipeline* pipeline = new MockTradingPipeline(name, false, 0, "異常執行");
        pipeline.SetThrowException(true);
        return pipeline;
    }

    // 創建數據饋送流水線
    static MockTradingPipeline* CreateDataFeedPipeline(string name = "數據饋送")
    {
        return new MockTradingPipeline(name, true, 50, "數據饋送完成", "DataFeedPipeline");
    }

    // 創建信號分析流水線
    static MockTradingPipeline* CreateSignalPipeline(string name = "信號分析")
    {
        return new MockTradingPipeline(name, true, 100, "信號分析完成", "SignalPipeline");
    }

    // 創建訂單處理流水線
    static MockTradingPipeline* CreateOrderPipeline(string name = "訂單處理")
    {
        return new MockTradingPipeline(name, true, 150, "訂單處理完成", "OrderPipeline");
    }

    // 創建風險控制流水線
    static MockTradingPipeline* CreateRiskPipeline(string name = "風險控制")
    {
        return new MockTradingPipeline(name, true, 75, "風險檢查完成", "RiskPipeline");
    }

    // 創建日誌記錄流水線
    static MockTradingPipeline* CreateLogPipeline(string name = "日誌記錄")
    {
        return new MockTradingPipeline(name, true, 25, "日誌記錄完成", "LogPipeline");
    }

    // 創建批量流水線
    static void CreateBatchPipelines(MockTradingPipeline* &pipelines[], int count, bool allSuccessful = true)
    {
        ArrayResize(pipelines, count);

        for(int i = 0; i < count; i++)
        {
            string name = StringFormat("批量流水線_%d", i + 1);
            bool shouldSucceed = allSuccessful || (i % 2 == 0); // 如果不是全部成功，則奇偶交替
            pipelines[i] = new MockTradingPipeline(name, shouldSucceed, 10);
        }
    }

    // 清理批量流水線
    static void CleanupBatchPipelines(MockTradingPipeline* &pipelines[])
    {
        for(int i = 0; i < ArraySize(pipelines); i++)
        {
            if(pipelines[i] != NULL)
            {
                delete pipelines[i];
                pipelines[i] = NULL;
            }
        }
        ArrayResize(pipelines, 0);
    }
};

// 注意：MockCompositePipeline 需要在包含 CompositePipeline.mqh 後定義
// 這裡只提供工廠方法來創建複合流水線用於測試
