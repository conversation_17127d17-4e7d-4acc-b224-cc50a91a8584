//+------------------------------------------------------------------+
//|                                   RunTestPipelineGroupManager.mq4 |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含測試文件
#include "TestPipelineGroupManager.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== PipelineGroupManager 單元測試開始 ===");
    Print("測試時間: ", TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
    Print("");
    
    // 創建並運行測試
    TestPipelineGroupManager* test = new TestPipelineGroupManager();
    
    if(test != NULL)
    {
        test.RunTests();
        delete test;
    }
    else
    {
        Print("錯誤: 無法創建測試實例");
    }
    
    Print("");
    Print("=== PipelineGroupManager 單元測試完成 ===");
}
//+------------------------------------------------------------------+
