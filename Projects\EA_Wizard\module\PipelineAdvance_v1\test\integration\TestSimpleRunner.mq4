//+------------------------------------------------------------------+
//|                                              TestSimpleRunner.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "SimpleTestRunner.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 測試 SimpleTestRunner 編譯 ===");
    
    // 創建測試運行器
    SimpleIntegrationTestRunner* runner = new SimpleIntegrationTestRunner();
    
    // 運行測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("=== 測試完成 ===");
}
//+------------------------------------------------------------------+
