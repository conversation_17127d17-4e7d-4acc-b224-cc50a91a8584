//+------------------------------------------------------------------+
//|                                TradingPipelineContainerManager.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineContainer.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 簡化的交易流水線容器管理器                                       |
//| 使用統一的 TradingPipelineContainer 替代原有的複雜層次結構      |
//+------------------------------------------------------------------+
class TradingPipelineContainerManager
{
private:
    string m_name;                                      // 管理器名稱
    string m_type;                                      // 管理器類型
    Vector<TradingPipelineContainer*> m_containers;     // 容器向量
    bool m_owned;                                       // 是否擁有容器
    bool m_isEnabled;                                   // 是否啟用
    bool m_executed;                                    // 是否已執行
    int m_maxContainers;                                // 最大容器數量

public:
    // 構造函數
    TradingPipelineContainerManager(string name = "TradingPipelineContainerManager",
                                   string type = "ContainerManager",
                                   bool owned = false,
                                   int maxContainers = 10)
        : m_name(name),
          m_type(type),
          m_containers(owned),
          m_owned(owned),
          m_isEnabled(true),
          m_executed(false),
          m_maxContainers(maxContainers)
    {
    }

    // 析構函數
    virtual ~TradingPipelineContainerManager()
    {
        Clear();
    }

    // 添加容器
    bool AddContainer(TradingPipelineContainer* container)
    {
        if(container == NULL)
        {
            return false;
        }

        // 檢查是否已存在同名容器
        if(FindContainerByName(container.GetName()) != NULL)
        {
            return false;
        }

        // 檢查是否已達到最大數量
        if(m_containers.size() >= m_maxContainers)
        {
            return false;
        }

        return m_containers.add(container);
    }

    // 移除容器
    bool RemoveContainer(TradingPipelineContainer* container)
    {
        if(container == NULL)
        {
            return false;
        }

        return m_containers.remove(container);
    }

    // 按名稱移除容器
    bool RemoveContainerByName(string name)
    {
        TradingPipelineContainer* container = FindContainerByName(name);
        if(container != NULL)
        {
            return m_containers.remove(container);
        }
        return false;
    }

    // 按名稱查找容器
    TradingPipelineContainer* FindContainerByName(string name)
    {
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.GetName() == name)
            {
                return container;
            }
        }
        return NULL;
    }

    // 按事件類型查找容器
    TradingPipelineContainer* FindContainerByEventType(ENUM_TRADING_EVENT eventType)
    {
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.GetEventType() == eventType)
            {
                return container;
            }
        }
        return NULL;
    }

    // 獲取所有指定事件類型的容器
    int GetContainersByEventType(ENUM_TRADING_EVENT eventType, TradingPipelineContainer* &containers[])
    {
        Vector<TradingPipelineContainer*> result;
        
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.GetEventType() == eventType)
            {
                result.add(container);
            }
        }

        int count = result.size();
        ArrayResize(containers, count);
        
        for(int i = 0; i < count; i++)
        {
            containers[i] = result.get(i);
        }
        
        return count;
    }

    // 執行指定事件類型的所有容器
    void Execute(ENUM_TRADING_EVENT eventType)
    {
        if(!m_isEnabled)
        {
            return;
        }

        int executedCount = 0;
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.IsEnabled() && container.GetEventType() == eventType)
            {
                container.Execute();
                executedCount++;
            }
        }

        if(executedCount > 0)
        {
            m_executed = true;
        }
    }

    // 執行所有容器
    void ExecuteAll()
    {
        if(!m_isEnabled)
        {
            return;
        }

        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.IsEnabled())
            {
                container.Execute();
            }
        }

        m_executed = true;
    }

    // 重置指定事件類型的所有容器
    void Restore(ENUM_TRADING_EVENT eventType)
    {
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.GetEventType() == eventType)
            {
                container.Restore();
            }
        }

        // 檢查是否所有容器都已重置
        bool allRestored = true;
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.IsExecuted())
            {
                allRestored = false;
                break;
            }
        }

        if(allRestored)
        {
            m_executed = false;
        }
    }

    // 重置所有容器
    void RestoreAll()
    {
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            container.Restore();
        }
        m_executed = false;
    }

    // 清理所有容器
    void Clear()
    {
        m_containers.clear();
        m_executed = false;
    }

    // 獲取容器數量
    int GetContainerCount() const
    {
        return m_containers.size();
    }

    // 獲取最大容器數量
    int GetMaxContainers() const
    {
        return m_maxContainers;
    }

    // 按索引獲取容器
    TradingPipelineContainer* GetContainer(int index)
    {
        if(index >= 0 && index < m_containers.size())
        {
            return m_containers.get(index);
        }
        return NULL;
    }

    // 獲取所有容器
    int GetAllContainers(TradingPipelineContainer* &containers[])
    {
        int count = m_containers.size();
        ArrayResize(containers, count);

        for(int i = 0; i < count; i++)
        {
            containers[i] = m_containers.get(i);
        }

        return count;
    }

    // 獲取管理器名稱
    string GetName() const
    {
        return m_name;
    }

    // 獲取管理器類型
    string GetType() const
    {
        return m_type;
    }

    // 檢查是否已執行
    bool IsExecuted() const
    {
        return m_executed;
    }

    // 設置啟用狀態
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
    }

    // 檢查是否啟用
    bool IsEnabled() const
    {
        return m_isEnabled;
    }

    // 檢查是否有空位置
    bool HasEmptySlot() const
    {
        return m_containers.size() < m_maxContainers;
    }

    // 檢查是否為空
    bool IsEmpty() const
    {
        return m_containers.size() == 0;
    }

    // 檢查是否已滿
    bool IsFull() const
    {
        return m_containers.size() >= m_maxContainers;
    }

    // 獲取管理器狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat(
            "管理器名稱: %s\n"
            "類型: %s\n"
            "狀態: %s\n"
            "已執行: %s\n"
            "容器數量: %d/%d",
            m_name,
            m_type,
            m_isEnabled ? "啟用" : "禁用",
            m_executed ? "是" : "否",
            m_containers.size(),
            m_maxContainers
        );
        return info;
    }

    // 啟用指定事件類型的所有容器
    void EnableContainersByEventType(ENUM_TRADING_EVENT eventType, bool enabled = true)
    {
        foreachv(TradingPipelineContainer*, container, m_containers)
        {
            if(container.GetEventType() == eventType)
            {
                container.SetEnabled(enabled);
            }
        }
    }

    // 獲取指定事件類型的容器數量
    int GetContainerCountByEventType(ENUM_TRADING_EVENT eventType) const
    {
        int count = 0;
        foreachv(TradingPipelineContainer*, container, (Vector<TradingPipelineContainer*>*)GetPointer(m_containers))
        {
            if(container.GetEventType() == eventType)
            {
                count++;
            }
        }
        return count;
    }
};
