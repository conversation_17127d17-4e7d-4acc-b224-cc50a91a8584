//+------------------------------------------------------------------+
//|                                  TradingPipelineContainerTest.mq4 |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineContainer.mqh"
#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單流水線實現                                           |
//+------------------------------------------------------------------+
class TestPipeline : public TradingPipeline
{
private:
    string m_testMessage;
    bool m_shouldFail;

public:
    TestPipeline(string name, string testMessage = "測試執行", bool shouldFail = false)
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage),
          m_shouldFail(shouldFail)
    {
    }

protected:
    virtual void Main() override
    {
        if(m_shouldFail)
        {
            Print(StringFormat("[%s] 模擬執行失敗: %s", GetName(), m_testMessage));
            // 模擬失敗但不影響執行狀態
        }
        else
        {
            Print(StringFormat("[%s] 執行成功: %s", GetName(), m_testMessage));
        }
    }
};

//+------------------------------------------------------------------+
//| 測試主函數                                                       |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== TradingPipelineContainer 統一容器測試開始 ===");
    
    // 測試 1: 創建容器和基本操作
    Print("\n--- 測試 1: 創建容器和基本操作 ---");
    TradingPipelineContainer* container = new TradingPipelineContainer(
        "主容器",
        "用於測試的主要容器",
        "TestContainer",
        TRADING_TICK,
        false,
        10
    );
    
    Print("容器名稱: ", container.GetName());
    Print("容器描述: ", container.GetDescription());
    Print("事件類型: ", container.GetEventType());
    Print("是否啟用: ", container.IsEnabled());
    Print("是否為空: ", container.IsEmpty());
    Print("最大容量: ", container.GetMaxPipelines());
    
    // 測試 2: 添加子流水線
    Print("\n--- 測試 2: 添加子流水線 ---");
    TestPipeline* pipeline1 = new TestPipeline("數據獲取", "獲取市場數據");
    TestPipeline* pipeline2 = new TestPipeline("信號分析", "分析交易信號");
    TestPipeline* pipeline3 = new TestPipeline("風險控制", "執行風險控制");
    
    bool added1 = container.AddPipeline(pipeline1);
    bool added2 = container.AddPipeline(pipeline2);
    bool added3 = container.AddPipeline(pipeline3);
    
    Print("添加流水線1結果: ", added1);
    Print("添加流水線2結果: ", added2);
    Print("添加流水線3結果: ", added3);
    Print("容器中流水線數量: ", container.GetPipelineCount());
    
    // 測試 3: 查找功能
    Print("\n--- 測試 3: 查找功能 ---");
    ITradingPipeline* found = container.FindByName("信號分析");
    Print("查找'信號分析'結果: ", found != NULL ? found.GetName() : "未找到");
    
    ITradingPipeline* notFound = container.FindByName("不存在的流水線");
    Print("查找'不存在的流水線'結果: ", notFound != NULL ? notFound.GetName() : "未找到");
    
    bool hasSignal = container.HasPipelineByName("信號分析");
    bool hasNonExist = container.HasPipelineByName("不存在");
    Print("是否包含'信號分析': ", hasSignal);
    Print("是否包含'不存在': ", hasNonExist);
    
    // 測試 4: 執行流水線
    Print("\n--- 測試 4: 執行流水線 ---");
    Print("執行前狀態:");
    Print("  - 容器已執行: ", container.IsExecuted());
    Print("  - 流水線1已執行: ", pipeline1.IsExecuted());
    Print("  - 流水線2已執行: ", pipeline2.IsExecuted());
    Print("  - 流水線3已執行: ", pipeline3.IsExecuted());
    
    container.Execute();
    
    Print("執行後狀態:");
    Print("  - 容器已執行: ", container.IsExecuted());
    Print("  - 流水線1已執行: ", pipeline1.IsExecuted());
    Print("  - 流水線2已執行: ", pipeline2.IsExecuted());
    Print("  - 流水線3已執行: ", pipeline3.IsExecuted());
    
    // 測試 5: 重置功能
    Print("\n--- 測試 5: 重置功能 ---");
    container.Restore();
    
    Print("重置後狀態:");
    Print("  - 容器已執行: ", container.IsExecuted());
    Print("  - 流水線1已執行: ", pipeline1.IsExecuted());
    Print("  - 流水線2已執行: ", pipeline2.IsExecuted());
    Print("  - 流水線3已執行: ", pipeline3.IsExecuted());
    
    // 測試 6: 移除功能
    Print("\n--- 測試 6: 移除功能 ---");
    bool removed = container.RemovePipelineByName("信號分析");
    Print("移除'信號分析'結果: ", removed);
    Print("移除後流水線數量: ", container.GetPipelineCount());
    
    // 測試 7: 容器管理器
    Print("\n--- 測試 7: 容器管理器 ---");
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("主管理器");
    
    // 創建不同事件類型的容器
    TradingPipelineContainer* initContainer = new TradingPipelineContainer(
        "初始化容器", "處理初始化事件", "InitContainer", TRADING_INIT);
    TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
        "Tick容器", "處理Tick事件", "TickContainer", TRADING_TICK);
    TradingPipelineContainer* deinitContainer = new TradingPipelineContainer(
        "清理容器", "處理清理事件", "DeinitContainer", TRADING_DEINIT);
    
    bool managerAdded1 = manager.AddContainer(initContainer);
    bool managerAdded2 = manager.AddContainer(tickContainer);
    bool managerAdded3 = manager.AddContainer(deinitContainer);
    
    Print("添加初始化容器結果: ", managerAdded1);
    Print("添加Tick容器結果: ", managerAdded2);
    Print("添加清理容器結果: ", managerAdded3);
    Print("管理器中容器數量: ", manager.GetContainerCount());
    
    // 測試 8: 按事件類型查找和執行
    Print("\n--- 測試 8: 按事件類型查找和執行 ---");
    TradingPipelineContainer* foundTick = manager.FindContainerByEventType(TRADING_TICK);
    Print("查找TRADING_TICK容器: ", foundTick != NULL ? foundTick.GetName() : "未找到");
    
    // 向容器添加測試流水線
    TestPipeline* tickPipeline = new TestPipeline("Tick處理", "處理Tick數據");
    if(foundTick != NULL)
    {
        foundTick.AddPipeline(tickPipeline);
        Print("向Tick容器添加流水線，數量: ", foundTick.GetPipelineCount());
    }
    
    // 執行指定事件類型
    Print("執行TRADING_TICK事件...");
    manager.Execute(TRADING_TICK);
    
    Print("執行後狀態:");
    Print("  - 管理器已執行: ", manager.IsExecuted());
    if(foundTick != NULL)
    {
        Print("  - Tick容器已執行: ", foundTick.IsExecuted());
        Print("  - Tick流水線已執行: ", tickPipeline.IsExecuted());
    }
    
    // 測試 9: 狀態信息
    Print("\n--- 測試 9: 狀態信息 ---");
    Print("容器狀態信息:");
    Print(container.GetStatusInfo());
    
    Print("\n管理器狀態信息:");
    Print(manager.GetStatusInfo());
    
    // 測試 10: 啟用/禁用功能
    Print("\n--- 測試 10: 啟用/禁用功能 ---");
    container.SetEnabled(false);
    Print("禁用容器後，是否啟用: ", container.IsEnabled());
    
    container.Execute(); // 應該跳過執行
    Print("禁用狀態下執行結果: ", container.GetResult().GetMessage());
    
    container.SetEnabled(true);
    Print("重新啟用容器後，是否啟用: ", container.IsEnabled());
    
    // 清理資源
    Print("\n--- 清理測試資源 ---");
    delete container;
    delete manager;
    delete pipeline1;
    delete pipeline2;
    delete pipeline3;
    delete tickPipeline;
    delete initContainer;
    delete tickContainer;
    delete deinitContainer;
    
    Print("\n=== TradingPipelineContainer 統一容器測試完成 ===");
}

//+------------------------------------------------------------------+
//| 專家顧問初始化函數                                               |
//+------------------------------------------------------------------+
int OnInit()
{
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 專家顧問反初始化函數                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| 專家顧問tick函數                                                 |
//+------------------------------------------------------------------+
void OnTick()
{
}
