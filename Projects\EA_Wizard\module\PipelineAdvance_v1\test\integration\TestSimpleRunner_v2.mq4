//+------------------------------------------------------------------+
//|                                              TestSimpleRunner_v2.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "SimpleTestRunner_v2.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 測試 SimpleTestRunner_v2 編譯 ===");
    
    // 創建測試運行器
    SimpleIntegrationTestRunner_v2* runner = new SimpleIntegrationTestRunner_v2();
    
    // 運行測試
    runner.RunIntegrationTests();
    
    // 清理
    delete runner;
    
    Print("=== 測試完成 ===");
}
//+------------------------------------------------------------------+
