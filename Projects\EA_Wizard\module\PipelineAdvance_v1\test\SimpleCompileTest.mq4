//+------------------------------------------------------------------+
//|                                            SimpleCompileTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 只測試基本的包含是否成功
#include "../TradingEvent.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("基本編譯測試成功！");
    
    // 測試枚舉
    ENUM_TRADING_EVENT event = TRADING_TICK;
    ENUM_TRADING_STAGE stage = TICK_DATA_FEED;
    
    Print("事件: ", TradingEventUtils::EventToString(event));
    Print("階段: ", TradingEventUtils::StageToString(stage));
    
    Print("基本測試完成！");
}
//+------------------------------------------------------------------+
