//+------------------------------------------------------------------+
//|                                          ManagerCompileTest.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 測試 PipelineGroupManager 編譯
#include "../TradingEvent.mqh"
#include "../TradingPipeline.mqh"

// 先測試不包含 Vector 的部分
class SimplePipeline : public TradingPipeline
{
public:
    SimplePipeline(string name) : TradingPipeline(name, "SimplePipeline", TICK_DATA_FEED, NULL) {}

protected:
    virtual void Main() override
    {
        Print("執行簡單流水線: ", GetName());
    }
};

// 現在測試 PipelineGroupManager
#include "../PipelineGroupManager.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== PipelineGroupManager 編譯測試開始 ===");
    
    // 測試 1: 創建管理器
    Print("\n--- 測試 1: 創建管理器 ---");
    PipelineGroupManager* manager = new PipelineGroupManager("測試管理器");
    Print("管理器名稱: ", manager.GetName());
    Print("管理器類型: ", manager.GetType());
    Print("最大組數量: ", manager.GetMaxGroups());
    Print("當前組數量: ", manager.GetGroupCount());
    Print("是否有空位置: ", manager.HasEmptySlot());
    
    // 測試 2: 創建流水線
    Print("\n--- 測試 2: 創建流水線 ---");
    SimplePipeline* pipeline1 = new SimplePipeline("流水線1");
    SimplePipeline* pipeline2 = new SimplePipeline("流水線2");
    
    Print("流水線1名稱: ", pipeline1.GetName());
    Print("流水線2名稱: ", pipeline2.GetName());
    
    // 清理
    delete manager;
    delete pipeline1;
    delete pipeline2;
    
    Print("\n=== PipelineGroupManager 編譯測試完成 ===");
}
//+------------------------------------------------------------------+
