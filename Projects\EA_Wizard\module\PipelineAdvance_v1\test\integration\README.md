# PipelineAdvance_v1 整合測試

本目錄包含 PipelineAdvance_v1 模組的整合測試，用於驗證模組各組件之間的協作和整體功能。

## 📁 文件結構

```
integration/
├── README.md                                    # 本文檔
├── MockTradingPipeline.mqh                     # 模擬交易流水線
├── TestPipelineAdvanceV1Integration.mqh        # 主要整合測試類
├── TestRunnerWithDocuments.mqh                 # 支援文檔輸出的測試運行器
└── RunIntegrationTests.mq4                     # 整合測試執行腳本
```

## 🧪 測試場景

### 1. 基本工作流程測試 (TestBasicWorkflow)

- 測試 TradingPipeline → CompositePipeline → PipelineGroup 的完整流程
- 驗證基本的流水線執行順序和狀態管理
- 確保所有組件正確協作

### 2. 複雜嵌套結構測試 (TestComplexNestedStructure)

- 測試多層嵌套的複合流水線結構
- 驗證深層嵌套的執行邏輯
- 確保嵌套結構的正確性

### 3. 錯誤處理測試 (TestErrorHandling)

- 測試包含失敗流水線的處理
- 驗證異常情況的處理機制
- 確保錯誤不會影響整體系統穩定性

### 4. 混合結果測試 (TestMixedResults)

- 測試成功和失敗流水線混合的場景
- 驗證複雜結果的處理邏輯
- 確保混合結果的正確統計

### 5. 大規模場景測試 (TestLargeScaleScenario)

- 測試大量流水線的執行性能
- 驗證系統在高負載下的穩定性
- 測量執行時間和資源使用

### 6. 事件類型處理測試 (TestEventTypeHandling)

- 測試不同交易事件類型的處理
- 驗證 TRADING_INIT、TRADING_TICK、TRADING_DEINIT 事件
- 確保事件類型的正確分類和處理

### 7. 邊界情況測試 (TestEdgeCases)

- 測試空流水線組和空複合流水線
- 驗證單個流水線的複合結構
- 測試重複執行防護和重置功能

### 8. 性能場景測試 (TestPerformanceScenario)

- 測試大量快速執行流水線的性能
- 測量平均執行時間
- 驗證性能指標

## 🚀 運行測試

### 運行所有整合測試

```mql4
#include "Projects/EA_Wizard/module/PipelineAdvance_v1/test/integration/RunIntegrationTests.mq4"

void OnStart()
{
    // 腳本會自動運行所有整合測試
}
```

### 運行特定測試場景

```mql4
#include "Projects/EA_Wizard/module/PipelineAdvance_v1/test/integration/TestRunnerWithDocuments.mqh"

void OnStart()
{
    // 只運行整合測試（無文檔輸出）
    RunIntegrationTestsOnly();

    // 運行整合測試並生成文檔
    RunIntegrationTestsWithDocuments();

    // 運行整合測試（無限制顯示）
    RunIntegrationTestsWithUnlimitedDisplay();

    // 快速整合測試檢查
    bool passed = QuickIntegrationCheck();
}
```

### 從主測試入口運行

```mql4
#include "Projects/EA_Wizard/module/PipelineAdvance_v1/test/RunAllTests.mqh"

void OnStart()
{
    // 運行所有測試（單元測試 + 整合測試）
    RunAllPipelineAdvanceV1Tests();

    // 運行所有測試並生成文檔
    RunAllPipelineAdvanceV1TestsWithDocs();

    // 只運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    // 快速測試檢查
    bool passed = QuickPipelineAdvanceV1Check();
}
```

## 📄 文檔輸出

整合測試支援自動生成詳細的測試報告文檔：

### 文檔特性

- **自動生成**：測試執行時自動創建文檔
- **詳細記錄**：包含每個測試的結果和錯誤信息
- **性能統計**：記錄執行時間和性能指標
- **可配置顯示**：支援限制顯示的測試結果數量

### 文檔配置選項

```mql4
// 生成文檔，顯示前10個結果
PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(true, 10);

// 生成文檔，無限制顯示
PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(true, -1);

// 不生成文檔
PipelineAdvanceV1TestRunnerWithDocs* runner = new PipelineAdvanceV1TestRunnerWithDocs(false);
```

### 文檔位置

生成的測試報告文檔位於：

```
MQL4/Files/PipelineAdvanceV1_Integration_Test_Report.txt
```

## 🔧 模擬組件

### MockTradingPipeline

提供用於測試的模擬交易流水線：

- **成功流水線**：模擬正常執行的流水線
- **失敗流水線**：模擬執行失敗的流水線
- **慢速流水線**：模擬執行時間較長的流水線
- **異常流水線**：模擬拋出異常的流水線

### MockTradingPipelineFactory

工廠類，用於創建各種類型的模擬流水線：

```mql4
// 創建成功的流水線
MockTradingPipeline* success = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功");

// 創建失敗的流水線
MockTradingPipeline* failed = MockTradingPipelineFactory::CreateFailedPipeline("失敗");

// 創建特定業務類型的流水線
MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();
MockTradingPipeline* order = MockTradingPipelineFactory::CreateOrderPipeline();
```

## 📊 測試統計

整合測試會提供詳細的統計信息：

- **總測試數**：執行的測試總數
- **通過數**：成功通過的測試數
- **失敗數**：失敗的測試數
- **成功率**：測試通過的百分比
- **執行時間**：各個測試場景的執行時間

## 🎯 最佳實踐

### 開發階段

- 使用無限制顯示 (`-1`) 查看所有測試詳情
- 重點關注失敗的測試案例
- 定期運行整合測試確保代碼質量

### 持續整合

- 使用快速檢查功能進行 CI/CD 驗證
- 設置適當的顯示限制以控制日誌大小
- 保存測試報告用於問題追蹤

### 性能監控

- 關注大規模場景和性能測試的結果
- 監控執行時間的變化趨勢
- 及時發現性能回歸問題

## 🔍 故障排除

### 常見問題

1. **文檔生成失敗**

   - 檢查 `MQL4\Files\` 目錄權限
   - 確保有足夠的磁盤空間
   - 檢查防毒軟件是否阻止文件創建

2. **測試執行緩慢**

   - 減少大規模測試的流水線數量
   - 調整模擬流水線的延遲時間
   - 使用快速檢查功能進行初步驗證

3. **記憶體使用過高**
   - 確保正確清理測試對象
   - 檢查是否有記憶體洩漏
   - 適當調整測試規模

### 調試技巧

- 使用 Print 語句追蹤執行流程
- 檢查測試報告文檔中的詳細錯誤信息
- 逐個運行測試場景定位問題

## 📝 版本歷史

- **v1.0.0**：初始版本，包含基本整合測試場景
- 支援文檔輸出功能
- 提供完整的模擬組件
- 包含性能測試和邊界情況測試
