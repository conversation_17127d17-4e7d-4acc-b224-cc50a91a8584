//+------------------------------------------------------------------+
//|                                      RunTestCompositePipeline.mq4 |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "TestCompositePipeline.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 開始執行 CompositePipeline 測試 ===");
    
    // 創建測試實例
    TestCompositePipeline* test = new TestCompositePipeline();
    
    // 運行測試
    test.RunTests();
    
    // 清理
    delete test;
    
    Print("=== CompositePipeline 測試完成 ===");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("測試程序結束");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 不需要在 tick 中執行任何操作
}
