//+------------------------------------------------------------------+
//|                                         RunIntegrationTests.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "SimpleTestRunner.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n======================================");
    Print("  PipelineAdvance_v1 整合測試套件");
    Print("======================================");
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print("測試類型: 整合測試");
    Print("======================================");

    // 運行整合測試
    RunIntegrationTests();

    Print("\n======================================");
    Print("  PipelineAdvance_v1 整合測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print("======================================");
}

//+------------------------------------------------------------------+
//| 運行整合測試                                                       |
//+------------------------------------------------------------------+
void RunIntegrationTests()
{
    Print("🚀 開始執行 PipelineAdvance_v1 整合測試...");

    // 創建測試運行器
    SimpleIntegrationTestRunner* runner = new SimpleIntegrationTestRunner();

    // 運行整合測試
    runner.RunIntegrationTests();

    // 清理
    delete runner;

    Print("✅ 整合測試執行完成");
}

//+------------------------------------------------------------------+
//| 快速整合測試檢查                                                   |
//+------------------------------------------------------------------+
bool QuickIntegrationCheck()
{
    Print("⚡ 開始快速整合測試檢查...");

    // 創建測試運行器
    SimpleIntegrationTestRunner* runner = new SimpleIntegrationTestRunner();

    // 運行整合測試
    runner.RunIntegrationTests();

    // 檢查結果
    bool allPassed = runner.AllTestsPassed();

    // 清理
    delete runner;

    if(allPassed)
    {
        Print("✅ 快速整合測試檢查通過");
    }
    else
    {
        Print("❌ 快速整合測試檢查失敗");
    }

    return allPassed;
}
