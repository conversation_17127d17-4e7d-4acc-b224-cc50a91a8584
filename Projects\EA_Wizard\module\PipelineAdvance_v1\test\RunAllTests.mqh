//+------------------------------------------------------------------+
//|                                                RunAllTests.mqh  |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "unit/TestCompositePipeline.mqh"
#include "unit/TestPipelineGroupManager.mqh"
#include "integration/SimpleTestRunner.mqh"
#include "integration/SimpleTestRunner_v2.mqh"

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//| 運行所有 PipelineAdvance_v1 測試                                  |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1Tests()
{
    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件");
    Print(StringRepeat("=", 70));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    // 運行增強版整合測試
    RunPipelineAdvanceV1IntegrationTestsV2();

    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));
}

//+------------------------------------------------------------------+
//| 運行單元測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1UnitTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 單元測試 ---");

    // 運行 CompositePipeline 測試
    TestCompositePipeline* compositePipelineTest = new TestCompositePipeline();
    compositePipelineTest.RunTests();
    delete compositePipelineTest;

    // 運行 PipelineGroupManager 測試
    TestPipelineGroupManager* pipelineGroupManagerTest = new TestPipelineGroupManager();
    pipelineGroupManagerTest.RunTests();
    delete pipelineGroupManagerTest;

    Print("--- PipelineAdvance_v1 單元測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行整合測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1IntegrationTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 整合測試 ---");

    // 創建整合測試運行器
    SimpleIntegrationTestRunner* runner = new SimpleIntegrationTestRunner();

    // 運行整合測試
    runner.RunIntegrationTests();

    // 清理
    delete runner;

    Print("--- PipelineAdvance_v1 整合測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行增強版整合測試 (v2)                                           |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1IntegrationTestsV2()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 增強版整合測試 (v2) ---");

    // 創建增強版整合測試運行器
    SimpleIntegrationTestRunner_v2* runner_v2 = new SimpleIntegrationTestRunner_v2();

    // 運行增強版整合測試
    runner_v2.RunIntegrationTests();

    // 清理
    delete runner_v2;

    Print("--- PipelineAdvance_v1 增強版整合測試 (v2) 完成 ---");
}

//+------------------------------------------------------------------+
//| 運行所有測試（簡化版）                                             |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1TestsSimple()
{
    Print("\n📄 開始執行 PipelineAdvance_v1 完整測試套件...");

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    // 運行增強版整合測試
    RunPipelineAdvanceV1IntegrationTestsV2();

    Print("✅ PipelineAdvance_v1 完整測試套件執行完成");
}

//+------------------------------------------------------------------+
//| 快速測試檢查                                                       |
//+------------------------------------------------------------------+
bool QuickPipelineAdvanceV1Check()
{
    Print("⚡ 開始快速 PipelineAdvance_v1 測試檢查...");

    bool unitTestsPassed = true;
    bool integrationTestsPassed = true;

    // 快速單元測試
    TestCompositePipeline* compositePipelineTest = new TestCompositePipeline();
    compositePipelineTest.RunTests();
    delete compositePipelineTest;

    TestPipelineGroupManager* pipelineGroupManagerTest = new TestPipelineGroupManager();
    pipelineGroupManagerTest.RunTests();
    delete pipelineGroupManagerTest;

    // 快速整合測試
    SimpleIntegrationTestRunner* integrationRunner = new SimpleIntegrationTestRunner();
    integrationRunner.RunIntegrationTests();
    integrationTestsPassed = integrationRunner.AllTestsPassed();
    delete integrationRunner;

    // 快速增強版整合測試
    SimpleIntegrationTestRunner_v2* integrationRunner_v2 = new SimpleIntegrationTestRunner_v2();
    integrationRunner_v2.RunIntegrationTests();
    bool integrationTestsV2Passed = integrationRunner_v2.AllTestsPassed();
    delete integrationRunner_v2;

    bool allPassed = unitTestsPassed && integrationTestsPassed && integrationTestsV2Passed;

    if(allPassed)
    {
        Print("✅ 快速測試檢查通過");
    }
    else
    {
        Print("❌ 快速測試檢查失敗");
    }

    return allPassed;
}

//+------------------------------------------------------------------+
//| 運行 SimpleTestRunner_v2 專用測試                                 |
//+------------------------------------------------------------------+
void RunSimpleTestRunnerV2Only()
{
    Print("\n🚀 開始執行 SimpleTestRunner_v2 專用測試...");

    // 創建增強版整合測試運行器
    SimpleIntegrationTestRunner_v2* runner_v2 = new SimpleIntegrationTestRunner_v2();

    // 運行增強版整合測試
    runner_v2.RunIntegrationTests();

    // 清理
    delete runner_v2;

    Print("✅ SimpleTestRunner_v2 專用測試執行完成");
}

//+------------------------------------------------------------------+
//| 比較 SimpleTestRunner 和 SimpleTestRunner_v2                     |
//+------------------------------------------------------------------+
void CompareSimpleTestRunners()
{
    Print("\n🔍 開始比較 SimpleTestRunner 和 SimpleTestRunner_v2...");

    Print("--- 運行原版 SimpleTestRunner ---");
    SimpleIntegrationTestRunner* runner_v1 = new SimpleIntegrationTestRunner();
    runner_v1.RunIntegrationTests();
    bool v1Passed = runner_v1.AllTestsPassed();
    delete runner_v1;

    Print("--- 運行增強版 SimpleTestRunner_v2 ---");
    SimpleIntegrationTestRunner_v2* runner_v2 = new SimpleIntegrationTestRunner_v2();
    runner_v2.RunIntegrationTests();
    bool v2Passed = runner_v2.AllTestsPassed();
    delete runner_v2;

    Print("--- 比較結果 ---");
    Print("原版 SimpleTestRunner 結果: " + (v1Passed ? "✅ 通過" : "❌ 失敗"));
    Print("增強版 SimpleTestRunner_v2 結果: " + (v2Passed ? "✅ 通過" : "❌ 失敗"));

    if(v1Passed && v2Passed)
    {
        Print("🎉 兩個版本都通過測試！");
    }
    else if(v1Passed && !v2Passed)
    {
        Print("⚠️ 原版通過但增強版失敗，需要檢查 v2 實現");
    }
    else if(!v1Passed && v2Passed)
    {
        Print("🔧 原版失敗但增強版通過，v2 可能修復了問題");
    }
    else
    {
        Print("❌ 兩個版本都失敗，需要檢查基礎實現");
    }

    Print("🔍 SimpleTestRunner 比較完成");
}

//+------------------------------------------------------------------+
//| PipelineGroupManager 專項測試                                     |
//+------------------------------------------------------------------+
void RunPipelineGroupManagerFocusedTests()
{
    Print("\n🎯 開始執行 PipelineGroupManager 專項測試...");

    Print("--- 單元測試：PipelineGroupManager ---");
    TestPipelineGroupManager* unitTest = new TestPipelineGroupManager();
    unitTest.RunTests();
    delete unitTest;

    Print("--- 整合測試：PipelineGroupManager (v2) ---");
    SimpleIntegrationTestRunner_v2* integrationTest = new SimpleIntegrationTestRunner_v2();
    integrationTest.RunIntegrationTests();
    delete integrationTest;

    Print("✅ PipelineGroupManager 專項測試完成");
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    // 默認運行所有測試
    RunAllPipelineAdvanceV1Tests();
}

