//+------------------------------------------------------------------+
//|                                        TestCompositePipelineFixed.mq4 |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "unit/TestCompositePipeline.mqh"

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 測試修正後的 CompositePipeline ===");
    
    // 創建測試運行器
    TestCompositePipeline* test = new TestCompositePipeline();
    
    // 運行測試
    test.RunTests();
    
    // 清理
    delete test;
    
    Print("=== CompositePipeline 測試完成 ===");
}
//+------------------------------------------------------------------+
